import { EventEmitter } from 'events'
import got, { GotEmitter } from 'got'
import nodeStream from 'stream'
import fs from 'fs-extra'

interface DownloadOptions {
	package_name: string
	cache_path: string
	cache_package_name: string
	url: string
}

export class Downloader extends EventEmitter {
	private package_name: string
	private readonly cache_path: string
	private url: string
	private cache_package_name: string
	// private stream?: CancelableRequest<Response<string>>

	constructor(options: DownloadOptions) {
		super()
		this.package_name = options.package_name
		this.cache_path = options.cache_path
	}

	start() {
		const stream = got.stream(this.url, {
			throwHttpErrors: false,
			encoding: null,
			timeout: { socket: 60000 }
		})
		stream.retryCount = 1
		this.bindListener(stream)
		const writeStream = fs.createWriteStream(this.cache_path)
		stream.pipe(writeStream)
	}

	private bindListener(stream: GotEmitter & nodeStream.Duplex) {
		stream.on('downloadProgress', (data) => {
			console.log(data.toString())
		})
	}
}
