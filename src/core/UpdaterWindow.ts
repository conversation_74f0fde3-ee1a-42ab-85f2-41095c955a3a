import { DEBUG, TEST } from '../env'
import { BrowserWindow, ipcMain } from 'electron'
import { EventEmitter } from 'events'
import MenuBuilder from './menu'
import { join } from 'path'
import { is } from '@electron-toolkit/utils'

/**
 * 更新器类 - 负责创建和管理更新窗口
 * 重构后移除了 @electron/remote 依赖，使用现代 Electron IPC 通信
 */
export default class UpdaterWindow extends EventEmitter {
	private window: BrowserWindow | null = null
	// 保留 dirname 参数以维持 API 兼容性，但当前实现中未使用
	// @ts-ignore: kept for API compatibility
	private readonly _dirname: string

	constructor(dirname: string) {
		super()
		this._dirname = dirname
	}

	/**
	 * 获取更新器窗口实例
	 */
	get win(): BrowserWindow | null {
		return this.window
	}

	/**
	 * 启动更新器窗口
	 */
	start(): void {
		// 创建更新器窗口
		const updateWindow = this.createUpdateWindow()
		this.window = updateWindow

		// 设置窗口事件监听器
		this.setupWindowEventListeners(updateWindow)

		// 加载更新器页面
		this.loadUpdaterPage(updateWindow)

		// 设置 IPC 通信
		this.setupIPC()

		// 设置菜单
		this.setupWindowMenu(updateWindow)

		// 存储窗口引用
		this.window = updateWindow

		// 开发模式下打开开发者工具
		// if (DEBUG || TEST) {
		// 	updateWindow.webContents.openDevTools()
		// }
	}

	/**
	 * 关闭更新器窗口
	 */
	close(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.close()
		}
	}

	/**
	 * 创建更新器窗口
	 */
	private createUpdateWindow(): BrowserWindow {
		return new BrowserWindow({
			width: 800,
			height: 600,
			resizable: false,
			center: true,
			frame: false,
			autoHideMenuBar: true,
			webPreferences: {
				preload: join(__dirname, '../preload/index.js'),
				sandbox: false
			}
		})
	}

	/**
	 * 设置窗口事件监听器
	 */
	private setupWindowEventListeners(window: BrowserWindow): void {
		window.on('closed', () => {
			console.log('updater window closed')
			this.window = null
			this.emit('window-closed')
		})

		// 监听窗口准备就绪事件
		window.webContents.once('did-finish-load', () => {
			this.emit('window-ready')
		})

		// 监听窗口加载失败事件
		window.webContents.on('did-fail-load', (_event, errorCode, errorDescription) => {
			console.error('Updater window failed to load:', errorCode, errorDescription)
			this.emit('window-load-failed', { errorCode, errorDescription })
		})
	}

	/**
	 * 加载更新器页面
	 */
	private loadUpdaterPage(window: BrowserWindow): void {
		// 使用与 main/index.ts 相同的逻辑来加载页面
		// HMR for renderer base on electron-vite cli.
		// Load the remote URL for development or the local html file for production.
		if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
			window.loadURL(process.env['ELECTRON_RENDERER_URL'])
		} else {
			window.loadFile(join(__dirname, '../renderer/index.html'))
		}
	}

	/**
	 * 设置 IPC 通信
	 * 替代原来的 MessageBridge，使用现代 Electron IPC 通信
	 */
	private setupIPC(): void {
		if (!this.window) return

		// 监听来自更新器窗口的 openMainWindow 请求
		const handleOpenMainWindow = (_event: any, options: { pack: string; data: any }) => {
			// 发射事件给外部监听器（保持向后兼容）
			this.emit('open-main-window', options)
		}

		// 设置 IPC 监听器，使用窗口 ID 作为唯一标识
		const channelName = `updater-open-main-window-${this.window.id}`
		ipcMain.on(channelName, handleOpenMainWindow)

		// 存储监听器引用以便清理
		this.window.once('closed', () => {
			ipcMain.removeListener(channelName, handleOpenMainWindow)
		})

		// 向更新器窗口发送通道名称，让它知道如何与主进程通信
		this.window.webContents.once('did-finish-load', () => {
			if (this.window && !this.window.isDestroyed()) {
				this.window.webContents.send('updater-ipc-channel', channelName)
			}
		})
	}

	/**
	 * 设置窗口菜单
	 */
	private setupWindowMenu(window: BrowserWindow): void {
		const menuBuilder = new MenuBuilder(window)
		menuBuilder.buildMenu()
	}

	/**
	 * 检查窗口是否已销毁
	 */
	isDestroyed(): boolean {
		return !this.window || this.window.isDestroyed()
	}

	/**
	 * 获取窗口ID（如果需要）
	 */
	getWindowId(): number | null {
		return this.window && !this.window.isDestroyed() ? this.window.id : null
	}

	/**
	 * 发送消息到更新器窗口
	 */
	sendMessage(channel: string, ...args: any[]): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.webContents.send(channel, ...args)
		}
	}

	/**
	 * 显示窗口
	 */
	show(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.show()
		}
	}

	/**
	 * 隐藏窗口
	 */
	hide(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.hide()
		}
	}

	/**
	 * 聚焦窗口
	 */
	focus(): void {
		if (this.window && !this.window.isDestroyed()) {
			this.window.focus()
		}
	}
}
